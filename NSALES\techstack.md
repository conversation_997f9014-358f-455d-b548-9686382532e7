# Tech Stack Summary for OMNIPOS Project

## Executive Summary

This project is a desktop Point of Sale (POS) application built using web technologies. It leverages **NW.js** to run an HTML/CSS/JavaScript frontend as a native desktop application. The backend is a **Node.js** server that provides an API for communicating with a **Microsoft SQL Server** database.

---

## Tech Stack Breakdown

### 1. Desktop Application Framework

*   **NW.js (Node-Webkit)**: The core framework that packages the web application (HTML, CSS, JS) into a native desktop application for Windows/Linux. It provides access to native OS-level APIs.
    *   **Evidence**: Usage of `require('nw.gui')`, `nw.Window.get()`, `require('fs')`, and `require('os')` in `welcome.html`.

### 2. Frontend (Client-Side)

*   **HTML5 & CSS3**: Standard for structuring and styling the user interface.
*   **JavaScript (ES5/ES6)**: The primary language for client-side logic and interactivity.
*   **jQuery (v2.2.4)**: The main JavaScript library used for DOM manipulation, event handling, and making AJAX calls to the backend API.
    *   **Evidence**: `jquery-2.2.4.min.js` is included, and its syntax (`$()`, `$.ajax()`) is used extensively throughout the JavaScript files.
*   **Bootstrap (v3.4)**: A CSS framework used for creating the layout, UI components (like modals, buttons, navbars), and ensuring a responsive design.
    *   **Evidence**: Inclusion of `bootstrap.min.css` and Bootstrap-specific CSS classes in the HTML files.
*   **Moment.js (v2.29.4)**: A library for parsing, validating, manipulating, and formatting dates and times. The project includes a `README.md` for this library which notes it is a legacy project.
    *   **Evidence**: `moment.min.js` is included in `welcome.html`.
*   **SweetAlert2**: Used to create beautiful, responsive, and customizable alert pop-ups.
    *   **Evidence**: `sweetalert2.min.js` and `sweetalert2.min.css` are included.
*   **Bootstrap Datepicker**: A UI component for adding a date picker to form fields.
    *   **Evidence**: `bootstrap-datepicker.js` and its associated CSS are included.
*   **Socket.IO**: A library for real-time, bidirectional, and event-based communication. It is likely used for features requiring instant updates, such as communication with a Cash Teller Machine (CTM).
    *   **Evidence**: `socket.io.min.js` is included in `welcome.html`.

### 3. Backend (Server-Side)

*   **Node.js**: The JavaScript runtime environment used to build the server-side API. It handles requests from the NW.js client, processes business logic, and interacts with the database.
    *   **Evidence**: The project structure and the use of `require()` syntax are characteristic of a Node.js application. The file `stspos.js` (referenced in comments) is likely the main server file.
*   **Custom API Layer**: The project does not appear to use a standard backend framework (like Express.js) or an ORM. Instead, it features a custom-built API that accepts raw SQL queries from the client via functions like `ajaxGetQuery` and `ajaxExec`.
    *   **Evidence**: JavaScript files like `creturn.js` construct and send raw SQL strings to the server for execution.

### 4. Database

*   **Microsoft SQL Server (MSSQL)**: The relational database management system used to store all application data.
    *   **Evidence**: The SQL syntax used throughout the project is T-SQL, which is specific to Microsoft SQL Server. Examples include `with(nolock)`, the `exec` command for stored procedures (e.g., `exec upfpos_...`), and the `dbo.` schema prefix.

### 5. Development & Environment

*   **File System (fs)**: The Node.js `fs` module is used for reading configuration files like `terminal.json` and `dev_env.json`, and for writing log files.
    *   **Evidence**: `require('fs')` and `fs.readFileSync()` are used in `welcome.html`.
*   **Operating System (os)**: The Node.js `os` module is used to get information about the host machine, such as hostname and network interfaces.
    *   **Evidence**: `require('os')` and `os.hostname()` are used in `welcome.html`.
