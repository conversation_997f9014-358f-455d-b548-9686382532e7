// ฟังก์ชันสร้าง PKCE code verifier และ challenge
async function generatePKCE() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    const code_verifier = btoa(String.fromCharCode(...array))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

    const encoder = new TextEncoder();
    const data = encoder.encode(code_verifier);
    const digest = await crypto.subtle.digest('SHA-256', data);
    const base64Digest = btoa(String.fromCharCode(...new Uint8Array(digest)))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

    return {
        code_verifier,
        code_challenge: base64Digest
    };
}
