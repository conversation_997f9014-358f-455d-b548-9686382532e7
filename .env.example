# OMNIPOS Environment Configuration
# Copy this file to .env and update with your actual values

# Database Configuration
DB_HOST=your_database_server
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=upfnwpos

# Store Configuration
STORE_CODE=920
LOCK_NUMBER=999

# Server Endpoints
WEB_SERVER=http://localhost:94
CLD_SERVER=localhost
SUPERVISE_URL=http://localhost:96
GW_SERVER=http://localhost:94
OMNIPOS_API_URL=http://localhost:3009

# Hardware Configuration
PRINTER_NAME=BIXOLON SRP-330II
EDC_MACHINE=EMULATE

# Debug Mode
DEBUG=true