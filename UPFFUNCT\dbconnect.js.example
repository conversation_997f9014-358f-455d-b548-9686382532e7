const tds 	=require("tedious")
const msConfig= {
	host: process.env.DB_HOST || "localhost",
	userid: process.env.DB_USER || "your_db_user",
	password: process.env.DB_PASSWORD || "your_db_password",
	 options:{
			   database: process.env.DB_NAME || "upfnwpos",
			   encrypt: false,
			   connectTimeout:500000
		   }
	}
var dbServer = process.env.DB_HOST || "localhost"
exports.setDbServer = function(svr){
	dbServer=svr;
	return('Set DBServer to ' +svr);
}

exports.dbGetQuery = function(sql){
   try { 
	   var dfresp ={
				dbcode:999,
				dbmessage:"Start",
				dbrows:0,
				dbfields:null,
		   		dbitems:null
            }
	  // msConfig = config.get('mssqlConfig');
	   var jsn =getMsQuery(sql)
       return(jsn);
   }
   catch(ex){
	   console.log('dbGetQuery ['+sql +'] return Error '+ex.message);
	   dfresp.dbmessage="dbGetQuery "+ex.message;
	   return(dfresp);
   }
}  

function getMsQuery(sql){

	return new Promise((resolve,reject)=>{
		getMsQuery2(sql,(ret)=>{
			resolve(ret);
		});
	});
} 

function   getMsQuery2(sql,callback){
 // console.log("call getMsquery 2 %s",sql);
 	try
	{
		 var connection = new tds.Connection(msConfig);
		connection.on('connect', function(err) {
    	// If no error, then good to proceed.
		if (err) { 
			console.log("tedious db Error Conn");
			console.log(err);
			connection.close();
			var errresp = { dbcode: 999,
							dbmessage:err.message,
							dbrows:0,
							dbfields:[],
		   					dbitems:[]
			}
			callback(errresp);
		} else {
				console.log("Connected");
				var request = new tds.Request(sql, function(err) {
					if (err) {
						console.log(err);
						var errresp = { dbcode: 999,
							dbmessage:err.message,
							dbrows:0,
							dbfields:[]	,
		   					dbitems:[]
			}
			callback(errresp);
					}
					connection.close();
				});
				var result = "";
				var items=[];
			request.on('row', function(columns) {
				var item={};
				columns.forEach(function(column) {
					if (column.value === null) {
						//console.log('NULL');
						item[column.metadata.colName]='';
					} else {
						//result += column.value + " ";
						item[column.metadata.colName]=column.value;
					}
				});
				//result += "\n";
				items.push(item);
			});
			request.on('done', function(rowCount, more) {
				//console.log(rowCount + ' rows returned');
				var doneresp = { 
					dbcode: 0,
					dbmessage:"Completed",
					dbrows:rowCount,
					dbfields:result,
		   			dbitems:items
				}
				callback(doneresp);
			}); 
			connection.execSql(request);
		}
		});
	} catch(ex) {
	     console.log('dbConnect Error >>>' +ex.message);
		 var errresp = { dbcode: 999,
					dbmessage:ex.message,
					dbrows:0,
					dbfields:[],
		   			dbitems:[]
		}
		callback(errresp);
	}
}