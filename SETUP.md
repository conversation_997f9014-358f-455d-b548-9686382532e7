# OMNIPOS Setup Guide

## Initial Setup

### 1. Environment Configuration
Copy the example configuration files and update with your values:

```bash
cp terminal.json.example terminal.json
cp UPFFUNCT/dbconnect.js.example UPFFUNCT/dbconnect.js
cp .env.example .env
```

### 2. Update Configuration Files

**terminal.json:**
- Update `storeCode`, `lockNumber` with your store information
- Set correct server URLs for your environment
- Configure printer settings

**UPFFUNCT/dbconnect.js:**
- Update database connection settings
- Set proper credentials for your SQL Server

**.env:**
- Set all environment variables for your deployment

### 3. Security Notes

⚠️ **IMPORTANT**: Never commit the following files to git:
- `terminal.json` (contains store-specific configuration)
- `UPFFUNCT/dbconnect.js` (contains database credentials)
- `.env` (contains sensitive environment variables)
- Any log files or transaction data

These files are already included in `.gitignore` for your protection.

### 4. Database Setup
Ensure your SQL Server is configured with:
- Database: `upfnwpos` (or as specified in your config)
- User with appropriate permissions for POS operations
- Required stored procedures with `upfpos_*` naming convention

### 5. Hardware Setup
- Configure printers (BIXOLON, Toshiba supported)
- Set up EDC terminals for credit card processing
- Test smart card readers if applicable

### 6. Testing
- Run `npm run dev` to start in development mode
- Verify database connectivity
- Test printer functionality
- Validate payment integrations

## Development Workflow

### Git Commands
```bash
# Check status (sensitive files should not appear)
git status

# Add files to staging
git add .

# Commit changes
git commit -m "Your commit message"

# View commit history
git log --oneline
```

### Common Development Tasks
- **Start Development**: `npm run dev`
- **Build for Production**: `npm run prod`
- **View Logs**: Check `log/` directory for application logs
- **Test Printing**: Use utilities in dotnet/ directory